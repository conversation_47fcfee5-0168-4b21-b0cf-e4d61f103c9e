from fastapi import APIRouter, Depends, status, Request
from sqlalchemy.orm import Session
from controller.member_authentication_controller import MemberAuthenticationController
from db.db import get_db
from schemas.member import CoMemberCreate, LoginRequest, Provider, CoMemberCreateEnhanced

router = APIRouter()

member_auth_controller = MemberAuthenticationController()

# authentication Routes
@router.post("/register", status_code=status.HTTP_201_CREATED)
async def create(member: CoMemberCreateEnhanced, db: Session = Depends(get_db)):
    user = {}
    return member_auth_controller.create_member(member, user, db)

@router.post("/login", status_code=status.HTTP_200_OK)
async def login(payload: LoginRequest, db: Session = Depends(get_db)):
    return member_auth_controller.login(payload, db)

# auth0 social login routes
@router.post("/social-login", status_code=status.HTTP_200_OK)
async def login_redirect(provider: Provider):
    return member_auth_controller.login_redirect(provider.provider, provider.redirect_uri)

@router.get("/callback", status_code=status.HTTP_200_OK)
async def callback(request: Request, db: Session = Depends(get_db)):
    code = request.query_params.get("code")
    if code is None:
        return {"error": "Missing 'code' parameter in callback."}
    
    return member_auth_controller.callback(code, db)
