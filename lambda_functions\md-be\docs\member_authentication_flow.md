# Member Authentication API Guide

This document provides a guide for clients on how to use the API for member registration and login.

---

## 1. Member Registration

This endpoint allows a new member to create an account. The process creates a user in our authentication system and a corresponding member profile in our database.

### Endpoint

`POST /api/members/register`

### Request Body

To register a new member, you need to send their email address and a chosen password.

**Fields:**
- `loginEmail` (string, required): The member's unique email address. This will be used for logging in.
- `password` (string, required): The member's desired password.

**Example Request:**

```json
{
  "loginEmail": "<EMAIL>",
  "password": "a-very-secure-password"
}
```

### Successful Response (201 Created)

Upon successful registration, the API will return the newly created member's profile information and an authentication token. This token should be saved and used for any subsequent API calls that require the member to be authenticated.

**Example Response:**

```json
{
    "success": true,
    "message": "Member created successfully",
    "data": {
        "user": {
            "uuid": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
            "loginEmail": "<EMAIL>",
            "firstName": null,
            "lastName": null,
            "membershipTier": "lite",
            "communityStatus": "unverified",
            "personalBusinessEmail": null,
            "phone": null,
            "professionalTitle": null,
            "loginEmailVerified": false,
            "hasSeenFirstLoginMessage": false
        },
        "token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3Mi..."
    }
}
```

### Error Responses

- **`409 Conflict`**: This error occurs if a member with the provided `loginEmail` already exists in the system.

---

## 2. Member Login

This endpoint allows a registered member to sign in to their account and receive a new authentication token.

### Endpoint

`POST /api/members/login`

### Request Body

To log in, you must provide the member's registered email and password.

**Example Request:**

```json
{
  "loginEmail": "<EMAIL>",
  "password": "their-secure-password"
}
```

### Successful Response (200 OK)

A successful login returns the member's unique identifier (`uuid`), their email, and a new `token`. This token proves their identity and must be included in the `Authorization` header for all protected API requests (e.g., `Authorization: Bearer <token>`).

**Example Response:**

```json
{
    "success": true,
    "message": "Login successful",
    "data": {
        "uuid": "f0e9d8c7-b6a5-4321-fedc-ba9876543210",
        "email": "<EMAIL>",
        "token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3Mi..."
    }
}
```

### Error Responses

- **`401 Unauthorized`**: This error is returned if the provided `loginEmail` or `password` is incorrect, or if the member is not found in the system.