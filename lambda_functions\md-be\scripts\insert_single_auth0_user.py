import sys
import os
import json
import logging
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import Column, String, Boolean, TIMESTAMP, Integer, MetaData, Table, text

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import in a specific order to avoid circular imports
from db.connection_manager import initialize_connection_manager
from config import settings

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Define the table structure manually to avoid circular imports
def get_auth0_users_table(metadata):
    """Define the co_auth0_users table structure manually with exact column names"""
    return Table(
        "co_auth0_users",
        metadata,
        Column("userId", String(255), primary_key=True, key="userId"),
        Column("email", String(255), nullable=False, key="email"),
        <PERSON>umn("emailVerified", Bo<PERSON>an, nullable=False, key="emailVerified"),
        Column("name", String(255), nullable=False, key="name"),
        Column("nickName", String(255), nullable=False, key="nickName"),
        Column("familyName", String(255), nullable=True, key="familyName"),
        Column("givenName", String(255), nullable=True, key="givenName"),
        Column("picture", String, nullable=False, key="picture"),
        Column("identities", String, nullable=False, key="identities"),  # We'll store JSON as string
        Column("locale", String(255), nullable=True, key="locale"),
        Column("userMetadata", String, nullable=False, key="userMetadata"),  # We'll store JSON as string
        Column("appMetadata", String, nullable=False, key="appMetadata"),  # We'll store JSON as string
        Column("lastIp", String(255), nullable=False, key="lastIp"),
        Column("loginsCount", Integer, nullable=False, key="loginsCount"),
        Column("createdAt", TIMESTAMP, nullable=False, key="createdAt"),
        Column("updatedAt", TIMESTAMP, nullable=False, key="updatedAt"),
        Column("lastLogin", TIMESTAMP, nullable=False, key="lastLogin")
    )

def insert_single_auth0_user():
    """Insert a single Auth0 user into the database"""
    # Initialize connection manager
    connection_manager = initialize_connection_manager(
        secret_name="stg_member_db_rw_user",
        region="us-east-1"
    )
    
    # Get database engine and create a session
    engine = connection_manager.get_engine()
    session_local = connection_manager.get_session_local()
    
    # Create a new session for this user
    row_session = session_local()
    
    # Create metadata and table definition
    metadata = MetaData()
    auth0_users = get_auth0_users_table(metadata)
    
    # User data to insert
    user_id = "auth0|68775f7a7c26f43522611bfc"
    email = "<EMAIL>"
    
    # Create identity JSON
    identity = [
        {
            "provider": "auth0",
            "user_id": "68775f7a7c26f43522611bfc",
            "connection": "Username-Password-Authentication"
        }
    ]
    
    try:
        # Check if user already exists
        existing_user = row_session.execute(
            text(f"SELECT \"userId\" FROM co_auth0_users WHERE \"userId\" = :user_id"),
            {"user_id": user_id}
        ).fetchone()
        
        if existing_user:
            logger.info(f"User {user_id} already exists, skipping")
            row_session.close()
            return
        
        # Current timestamp for created/updated fields
        current_time = datetime.now()
        
        # Prepare data for insertion
        user_data = {
            "userId": user_id,
            "email": email,
            "emailVerified": True,  # Assuming email is verified
            "name": "Glenn Example",  # Using a default name based on email
            "nickName": "Glenn",  # Using a default nickname
            "familyName": None,  # No family name provided
            "givenName": None,  # No given name provided
            "picture": "https://s.gravatar.com/avatar/default?s=480&r=pg&d=https%3A%2F%2Fcdn.auth0.com%2Favatars%2Fgl.png",  # Default picture
            "identities": json.dumps(identity),
            "locale": None,  # No locale provided
            "userMetadata": json.dumps({}),  # Empty user metadata
            "appMetadata": json.dumps({}),  # Empty app metadata
            "lastIp": "127.0.0.1",  # Default IP
            "loginsCount": 0,  # New user, no logins yet
            "createdAt": current_time,
            "updatedAt": current_time,
            "lastLogin": current_time
        }
        
        # Insert data
        row_session.execute(auth0_users.insert().values(**user_data))
        
        # Commit the record
        row_session.commit()
        logger.info(f"Successfully inserted user {user_id}")
        
    except Exception as e:
        row_session.rollback()
        logger.error(f"Error processing user {user_id}: {str(e)}")
    
    finally:
        row_session.close()

def main():
    logger.info("Starting single user import")
    insert_single_auth0_user()
    logger.info("Import completed")

if __name__ == "__main__":
    main()