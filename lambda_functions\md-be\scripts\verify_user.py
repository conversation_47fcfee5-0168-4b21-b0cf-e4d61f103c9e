import sys
import os
import json
import logging
from sqlalchemy import text

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import in a specific order to avoid circular imports
from db.connection_manager import initialize_connection_manager
from config import settings

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def verify_user(user_id):
    """Verify if a specific user exists in the database and display their details"""
    # Initialize connection manager
    connection_manager = initialize_connection_manager(
        secret_name="stg_member_db_rw_user",
        region="us-east-1"
    )
    
    # Get database engine and create a session
    session_local = connection_manager.get_session_local()
    db = session_local()
    
    try:
        # Query the user
        query = text("SELECT * FROM co_auth0_users WHERE \"userId\" = :user_id")
        result = db.execute(query, {"user_id": user_id})
        
        # Fetch the row
        row = result.fetchone()
        
        if row:
            logger.info(f"User {user_id} found in database")
            
            # Get the column names and values
            user_data = dict(row._mapping)
            
            # Print user details
            logger.info("User details:")
            for key, value in user_data.items():
                # Format JSON fields for better readability
                if key in ['identities', 'userMetadata', 'appMetadata'] and value:
                    try:
                        formatted_value = json.loads(value)
                        logger.info(f"  - {key}: {formatted_value}")
                    except:
                        logger.info(f"  - {key}: {value}")
                else:
                    logger.info(f"  - {key}: {value}")
        else:
            logger.info(f"User {user_id} not found in database")
    
    except Exception as e:
        logger.error(f"Error verifying user: {str(e)}")
    
    finally:
        db.close()

def main():
    # User ID to verify
    user_id = "auth0|68775f7a7c26f43522611bfc"
    
    logger.info(f"Verifying user {user_id}")
    verify_user(user_id)
    logger.info("Verification completed")

if __name__ == "__main__":
    main()