#!/usr/bin/env python3
"""
Test script to verify the bulk_upsert_members function fixes
"""

import sys
import os

# Add the lambda function path to sys.path
sys.path.append(os.path.join(os.path.dirname(__file__), 'lambda_functions', 'md-be'))

def test_imports():
    """Test that all imports work correctly"""
    try:
        from services.member_api import bulk_upsert_members
        from schemas.member import BulkUpsertMembersRequest, CoMemberBulkUpsert, MemberOperationResult
        print("✅ All imports successful")
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_schema_validation():
    """Test that the schemas work correctly"""
    try:
        from schemas.member import BulkUpsertMembersRequest, CoMemberBulkUpsert, MemberOperationResult
        from uuid import uuid4
        
        # Test CoMemberBulkUpsert schema
        member_data = CoMemberBulkUpsert(
            loginEmail="<EMAIL>",
            password="TestPass123!",
            firstName="Test",
            lastName="User"
        )
        print("✅ CoMemberBulkUpsert schema validation successful")
        
        # Test BulkUpsertMembersRequest schema
        request = BulkUpsertMembersRequest(members=[member_data])
        print("✅ BulkUpsertMembersRequest schema validation successful")
        
        # Test MemberOperationResult schema
        result = MemberOperationResult(
            loginEmail="<EMAIL>",
            success=True,
            action="created",
            message="Test message",
            memberUuid=uuid4()
        )
        print("✅ MemberOperationResult schema validation successful")
        
        return True
    except Exception as e:
        print(f"❌ Schema validation error: {e}")
        return False

def test_function_signature():
    """Test that the function signature is correct"""
    try:
        from services.member_api import bulk_upsert_members
        import inspect
        
        sig = inspect.signature(bulk_upsert_members)
        params = list(sig.parameters.keys())
        
        expected_params = ['request', 'db', 'admin_user_payload']
        if params == expected_params:
            print("✅ Function signature is correct")
            return True
        else:
            print(f"❌ Function signature mismatch. Expected: {expected_params}, Got: {params}")
            return False
    except Exception as e:
        print(f"❌ Function signature test error: {e}")
        return False

def main():
    """Run all tests"""
    print("Testing bulk_upsert_members function fixes...")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_schema_validation,
        test_function_signature
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! The bulk_upsert_members function should work correctly.")
    else:
        print("⚠️  Some tests failed. Please check the issues above.")

if __name__ == "__main__":
    main()
